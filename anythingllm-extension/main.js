/**
 * AnythingLLM Connector Extension - Main Entry Point
 * Geavanceerde integratie met AnythingLLM voor werkruimtes, chats, documenten en agents
 */

const AnythingLLMAPI = require('./utils/api');
const { getAvailableTemplates, getTemplate, createCustomAgent } = require('./utils/agent-templates');
const fs = require('fs').promises;
const path = require('path');

let api = null;
let config = null;

/**
 * Extension lifecycle - called when extension is loaded
 */
async function onLoad(context) {
  console.log("🚀 AnythingLLM Connector wordt geladen...");
  
  try {
    config = await context.getConfig();
    api = new AnythingLLMAPI(config.apiUrl, config.apiKey);
    
    // Test verbinding
    const isConnected = await api.testConnection();
    if (isConnected) {
      const workspaces = await api.getWorkspaces();
      console.log(`✅ Verbonden met AnythingLLM: ${workspaces.length || 0} werkruimtes gevonden`);
    } else {
      console.warn("⚠️  Kon niet verbinden met AnythingLLM - controleer API URL en key");
    }
  } catch (error) {
    console.error("❌ Fout bij laden AnythingLLM Connector:", error.message);
  }
}

/**
 * Format workspace data for display
 */
function formatWorkspaces(workspaces) {
  if (!workspaces || workspaces.length === 0) {
    return "### AnythingLLM Werkruimtes\n\n*Geen werkruimtes gevonden*";
  }
  
  return `### AnythingLLM Werkruimtes (${workspaces.length})\n\n` +
    workspaces.map(w => 
      `- **${w.name}** (Slug: \`${w.slug}\`)\n  ${w.description || '*Geen beschrijving*'}`
    ).join('\n\n');
}

/**
 * Format agents data for display
 */
function formatAgents(agents) {
  if (!agents || agents.length === 0) {
    return "### AnythingLLM Agents\n\n*Geen agents gevonden*";
  }
  
  return `### AnythingLLM Agents (${agents.length})\n\n` +
    agents.map(a => 
      `- **${a.name}** (ID: \`${a.id}\`)\n  ${a.description || '*Geen beschrijving*'}`
    ).join('\n\n');
}

/**
 * Extension commands
 */
const commands = {
  // ===== WORKSPACE COMMANDS =====
  
  "anythingllm.workspace.list": async (args, context) => {
    try {
      const workspaces = await api.getWorkspaces();
      return {
        result: workspaces,
        markdown: formatWorkspaces(workspaces.workspaces || workspaces)
      };
    } catch (error) {
      return { 
        error: `Fout bij ophalen werkruimtes: ${error.message}`,
        markdown: "### ❌ Fout\n\nKon werkruimtes niet ophalen. Controleer uw API verbinding."
      };
    }
  },
  
  "anythingllm.workspace.create": async (args, context) => {
    if (!args.name) {
      return { 
        error: "Werkruimtenaam is vereist",
        markdown: "### ❌ Fout\n\nGebruik: `anythingllm.workspace.create name=\"Mijn Werkruimte\" description=\"Optionele beschrijving\"`"
      };
    }
    
    try {
      const result = await api.createWorkspace(args.name, args.description || '');
      return {
        result,
        markdown: `### ✅ Werkruimte Aangemaakt\n\n**Naam:** ${result.workspace?.name || args.name}\n**Slug:** \`${result.workspace?.slug || 'onbekend'}\`\n**Beschrijving:** ${result.workspace?.description || args.description || '*Geen beschrijving*'}`
      };
    } catch (error) {
      return {
        error: `Fout bij aanmaken werkruimte: ${error.message}`,
        markdown: "### ❌ Fout\n\nKon werkruimte niet aanmaken. Controleer of de naam uniek is."
      };
    }
  },
  
  // ===== DOCUMENT COMMANDS =====
  
  "anythingllm.document.upload": async (args, context) => {
    if (!args.workspaceSlug) {
      return { 
        error: "Werkruimte slug is vereist",
        markdown: "### ❌ Fout\n\nGebruik: `anythingllm.document.upload workspaceSlug=\"mijn-werkruimte\" filePath=\"/pad/naar/bestand.txt\"`"
      };
    }
    
    if (!args.filePath && !args.content) {
      return { 
        error: "Bestandspad of content is vereist",
        markdown: "### ❌ Fout\n\nGeef een `filePath` of `content` parameter op."
      };
    }
    
    try {
      let file;
      let fileName;
      
      if (args.filePath) {
        // Upload from file path
        const fileData = await fs.readFile(args.filePath);
        fileName = path.basename(args.filePath);
        file = new File([fileData], fileName);
      } else {
        // Upload from content string
        fileName = args.fileName || 'document.txt';
        const blob = new Blob([args.content], { type: 'text/plain' });
        file = new File([blob], fileName);
      }
      
      const result = await api.uploadDocument(args.workspaceSlug, file);
      return {
        result,
        markdown: `### ✅ Document Geüpload\n\n**Bestand:** ${fileName}\n**Werkruimte:** \`${args.workspaceSlug}\`\n**Status:** ${result.success ? 'Succesvol' : 'Mislukt'}\n\n${result.message || ''}`
      };
    } catch (error) {
      return { 
        error: `Document uploaden mislukt: ${error.message}`,
        markdown: "### ❌ Fout\n\nKon document niet uploaden. Controleer het bestandspad en werkruimte slug."
      };
    }
  },
  
  // ===== CHAT COMMANDS =====
  
  "anythingllm.chat.start": async (args, context) => {
    if (!args.workspaceSlug) {
      return { 
        error: "Werkruimte slug is vereist",
        markdown: "### ❌ Fout\n\nGebruik: `anythingllm.chat.start workspaceSlug=\"mijn-werkruimte\"`"
      };
    }
    
    try {
      const history = await api.getChatHistory(args.workspaceSlug);
      const workspace = await api.getWorkspace(args.workspaceSlug);
      
      return {
        result: { history, workspace },
        markdown: `### 💬 Chat Gestart\n\n**Werkruimte:** ${workspace.workspace?.name || args.workspaceSlug}\n**Aantal berichten:** ${history.history?.length || 0}\n\n*Gebruik \`anythingllm.chat.message\` om berichten te versturen.*`
      };
    } catch (error) {
      return {
        error: `Chat starten mislukt: ${error.message}`,
        markdown: "### ❌ Fout\n\nKon chat niet starten. Controleer de werkruimte slug."
      };
    }
  },
  
  "anythingllm.chat.message": async (args, context) => {
    if (!args.workspaceSlug) {
      return { 
        error: "Werkruimte slug is vereist",
        markdown: "### ❌ Fout\n\nGebruik: `anythingllm.chat.message workspaceSlug=\"mijn-werkruimte\" message=\"Hallo!\"`"
      };
    }
    
    if (!args.message) {
      return { 
        error: "Bericht is vereist",
        markdown: "### ❌ Fout\n\nGeef een bericht op om te versturen."
      };
    }
    
    try {
      const result = await api.sendMessage(args.workspaceSlug, args.message, args.mode || 'chat');
      
      return {
        result,
        markdown: `### 💬 Chat Bericht\n\n**Jij:** ${args.message}\n\n**AnythingLLM:** ${result.textResponse || result.response || '*Geen antwoord ontvangen*'}\n\n---\n*Werkruimte: \`${args.workspaceSlug}\`*`
      };
    } catch (error) {
      return {
        error: `Bericht versturen mislukt: ${error.message}`,
        markdown: "### ❌ Fout\n\nKon bericht niet versturen. Controleer de werkruimte slug en probeer opnieuw."
      };
    }
  },

  // ===== AGENT COMMANDS =====

  "anythingllm.agent.list": async (args, context) => {
    try {
      // Note: This endpoint might not exist in all AnythingLLM versions
      // For now, we'll show available templates
      const templates = getAvailableTemplates();

      return {
        result: { templates },
        markdown: `### 🤖 Beschikbare Agent Templates\n\n${templates.map(t => `- **${t.name}** (\`${t.key}\`)\n  ${t.description}`).join('\n\n')}\n\n*Gebruik \`anythingllm.agent.create\` om een agent aan te maken.*`
      };
    } catch (error) {
      return {
        error: `Fout bij ophalen agents: ${error.message}`,
        markdown: "### ❌ Fout\n\nKon agents niet ophalen."
      };
    }
  },

  "anythingllm.agent.create": async (args, context) => {
    if (!args.template && !args.name) {
      const templates = getAvailableTemplates();
      return {
        result: { availableTemplates: templates },
        markdown: `### 🤖 Agent Aanmaken\n\n**Beschikbare Templates:**\n\n${templates.map(t => `- **${t.key}**: ${t.description}`).join('\n')}\n\n**Gebruik:**\n- Met template: \`anythingllm.agent.create template=\"customerService\" workspaceSlug=\"mijn-werkruimte\"\`\n- Custom: \`anythingllm.agent.create name=\"Mijn Agent\" description=\"Agent beschrijving\" workspaceSlug=\"mijn-werkruimte\"\``
      };
    }

    if (!args.workspaceSlug) {
      return {
        error: "Werkruimte slug is vereist",
        markdown: "### ❌ Fout\n\nGeef een werkruimte slug op waar de agent moet worden toegevoegd."
      };
    }

    try {
      let agentConfig;

      if (args.template) {
        const template = getTemplate(args.template);
        if (!template) {
          const available = getAvailableTemplates().map(t => t.key).join(', ');
          return {
            error: `Template '${args.template}' bestaat niet`,
            markdown: `### ❌ Fout\n\nBeschikbare templates: ${available}`
          };
        }
        agentConfig = template;
      } else {
        // Create custom agent
        agentConfig = createCustomAgent(
          args.name,
          args.description || '',
          args.goals ? args.goals.split(',').map(g => g.trim()) : [],
          args.constraints ? args.constraints.split(',').map(c => c.trim()) : [],
          args.tools ? args.tools.split(',').map(t => t.trim()) : [],
          args.personality || 'Professioneel en behulpzaam',
          []
        );
      }

      // For now, we'll create a system prompt that can be used in the workspace
      const systemPrompt = `Je bent ${agentConfig.name}: ${agentConfig.description}

DOELEN:
${agentConfig.config.goals.map(g => `- ${g}`).join('\n')}

BEPERKINGEN:
${agentConfig.config.constraints.map(c => `- ${c}`).join('\n')}

PERSOONLIJKHEID: ${agentConfig.config.personality}

BESCHIKBARE TOOLS: ${agentConfig.config.tools.join(', ')}

${agentConfig.config.examples.length > 0 ? `
VOORBEELDEN:
${agentConfig.config.examples.map(ex => `
Gebruiker: ${ex.input}
Agent: ${ex.output}
`).join('\n')}
` : ''}

Volg deze richtlijnen in al je interacties.`;

      // Update workspace with the agent system prompt
      const updateResult = await api.updateWorkspace(args.workspaceSlug, {
        openAiPrompt: systemPrompt
      });

      return {
        result: { agentConfig, systemPrompt, updateResult },
        markdown: `### ✅ Agent Aangemaakt\n\n**Naam:** ${agentConfig.name}\n**Beschrijving:** ${agentConfig.description}\n**Werkruimte:** \`${args.workspaceSlug}\`\n\n**Systeem Prompt geïnstalleerd in werkruimte.**\n\nDe agent is nu actief in de opgegeven werkruimte en zal volgens de geconfigureerde richtlijnen reageren.`
      };
    } catch (error) {
      return {
        error: `Agent aanmaken mislukt: ${error.message}`,
        markdown: "### ❌ Fout\n\nKon agent niet aanmaken. Controleer de werkruimte slug en probeer opnieuw."
      };
    }
  }
};

/**
 * Export module for extension system
 */
module.exports = {
  onLoad,
  commands: () => commands
};
