# AnythingLLM Connector Extension

Een geavanceerde extensie voor integratie met AnythingLLM, inclusief werkruimte beheer, document upload, chat functionaliteit en agent training.

## 🚀 Functies

### Werkruimte Beheer
- **`anythingllm.workspace.list`** - Toon alle beschikbare werkruimtes
- **`anythingllm.workspace.create`** - Maak nieuwe werkruimtes aan

### Document Beheer  
- **`anythingllm.document.upload`** - Upload documenten naar werkruimtes

### Chat Functionaliteit
- **`anythingllm.chat.start`** - Start een chat sessie in een werkruimte
- **`anythingllm.chat.message`** - Verstuur berichten naar AnythingLLM

### Agent Training
- **`anythingllm.agent.list`** - Toon beschikbare agent templates
- **`anythingllm.agent.create`** - Maak en configureer nieuwe agents

## ⚙️ Configuratie

De extensie gebruikt de volgende configuratie opties:

```json
{
  "apiUrl": "http://localhost:3001",
  "apiKey": "jouw-anythingllm-api-key"
}
```

## 📖 Gebruik Voorbeelden

### Werkruimtes
```javascript
// Toon alle werkruimtes
anythingllm.workspace.list()

// Maak nieuwe werkruimte
anythingllm.workspace.create({
  name: "Mijn Project",
  description: "Werkruimte voor project documentatie"
})
```

### Documenten
```javascript
// Upload bestand
anythingllm.document.upload({
  workspaceSlug: "mijn-project",
  filePath: "/pad/naar/document.pdf"
})

// Upload content direct
anythingllm.document.upload({
  workspaceSlug: "mijn-project", 
  content: "Dit is mijn document inhoud...",
  fileName: "notities.txt"
})
```

### Chat
```javascript
// Start chat
anythingllm.chat.start({
  workspaceSlug: "mijn-project"
})

// Verstuur bericht
anythingllm.chat.message({
  workspaceSlug: "mijn-project",
  message: "Wat staat er in de geüploade documenten?"
})
```

### Agents
```javascript
// Toon beschikbare templates
anythingllm.agent.list()

// Maak agent van template
anythingllm.agent.create({
  template: "customerService",
  workspaceSlug: "klantenservice"
})

// Maak custom agent
anythingllm.agent.create({
  name: "Mijn Custom Agent",
  description: "Gespecialiseerd in...",
  workspaceSlug: "mijn-project",
  goals: "Help gebruikers, Geef accurate info",
  constraints: "Wees beleefd, Vraag om verduidelijking"
})
```

## 🤖 Beschikbare Agent Templates

- **customerService** - Klantenservice ondersteuning
- **salesAgent** - Verkoop en product advisering  
- **technicalSupport** - Technische ondersteuning
- **contentCreator** - Content creatie en marketing
- **dataAnalyst** - Data analyse en rapportage
- **projectManager** - Project management en planning

## 🔧 Installatie

1. Plaats de extensie map in je extensies directory
2. Configureer je AnythingLLM API URL en key
3. Herstart je applicatie om de extensie te laden

## 📝 Opmerkingen

- Zorg ervoor dat je AnythingLLM instantie draait en toegankelijk is
- De API key moet de juiste permissies hebben voor werkruimte en document beheer
- Sommige functies vereisen specifieke AnythingLLM versies

## 🆘 Ondersteuning

Bij problemen:
1. Controleer je API verbinding met `anythingllm.workspace.list`
2. Verifieer je API key en URL configuratie
3. Check de console voor foutmeldingen
