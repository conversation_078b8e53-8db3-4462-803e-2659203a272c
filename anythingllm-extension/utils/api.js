/**
 * AnythingLLM API Client
 * Handles all API communication with AnythingLLM instance
 */

class AnythingLLMAPI {
  constructor(apiUrl, apiKey) {
    this.apiUrl = apiUrl.replace(/\/$/, ''); // Remove trailing slash
    this.apiKey = apiKey;
  }

  /**
   * Make a generic API request
   * @param {string} endpoint - API endpoint
   * @param {string} method - HTTP method
   * @param {object} data - Request data
   * @returns {Promise<object>} API response
   */
  async request(endpoint, method = 'GET', data = null) {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(`${this.apiUrl}${endpoint}`, options);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      } else {
        return { success: true, data: await response.text() };
      }
    } catch (error) {
      console.error(`API Request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // ===== WORKSPACE METHODS =====

  /**
   * Get all workspaces
   * @returns {Promise<Array>} List of workspaces
   */
  async getWorkspaces() {
    return this.request('/api/v1/workspaces');
  }

  /**
   * Create a new workspace
   * @param {string} name - Workspace name
   * @param {string} description - Workspace description
   * @returns {Promise<object>} Created workspace
   */
  async createWorkspace(name, description = '') {
    return this.request('/api/v1/workspaces', 'POST', { 
      name, 
      description 
    });
  }

  /**
   * Get workspace by slug
   * @param {string} slug - Workspace slug
   * @returns {Promise<object>} Workspace details
   */
  async getWorkspace(slug) {
    return this.request(`/api/v1/workspace/${slug}`);
  }

  /**
   * Update workspace settings
   * @param {string} slug - Workspace slug
   * @param {object} settings - New settings
   * @returns {Promise<object>} Updated workspace
   */
  async updateWorkspace(slug, settings) {
    return this.request(`/api/v1/workspace/${slug}`, 'POST', settings);
  }

  /**
   * Delete workspace
   * @param {string} slug - Workspace slug
   * @returns {Promise<object>} Deletion result
   */
  async deleteWorkspace(slug) {
    return this.request(`/api/v1/workspace/${slug}`, 'DELETE');
  }

  // ===== DOCUMENT METHODS =====

  /**
   * Upload document to workspace
   * @param {string} workspaceSlug - Workspace slug
   * @param {File|Blob} file - File to upload
   * @returns {Promise<object>} Upload result
   */
  async uploadDocument(workspaceSlug, file) {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch(`${this.apiUrl}/api/v1/workspace/${workspaceSlug}/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: formData
    });
    
    if (!response.ok) {
      throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  }

  /**
   * Get documents in workspace
   * @param {string} workspaceSlug - Workspace slug
   * @returns {Promise<Array>} List of documents
   */
  async getDocuments(workspaceSlug) {
    return this.request(`/api/v1/workspace/${workspaceSlug}/documents`);
  }

  /**
   * Remove document from workspace
   * @param {string} workspaceSlug - Workspace slug
   * @param {string} docname - Document name
   * @returns {Promise<object>} Removal result
   */
  async removeDocument(workspaceSlug, docname) {
    return this.request(`/api/v1/workspace/${workspaceSlug}/documents`, 'DELETE', {
      names: [docname]
    });
  }

  // ===== CHAT METHODS =====

  /**
   * Get chat history for workspace
   * @param {string} workspaceSlug - Workspace slug
   * @returns {Promise<Array>} Chat history
   */
  async getChatHistory(workspaceSlug) {
    return this.request(`/api/v1/workspace/${workspaceSlug}/chats`);
  }

  /**
   * Send message to workspace
   * @param {string} workspaceSlug - Workspace slug
   * @param {string} message - Message to send
   * @param {string} mode - Chat mode ('chat' or 'query')
   * @returns {Promise<object>} Chat response
   */
  async sendMessage(workspaceSlug, message, mode = 'chat') {
    return this.request(`/api/v1/workspace/${workspaceSlug}/chat`, 'POST', { 
      message,
      mode
    });
  }

  /**
   * Stream chat response
   * @param {string} workspaceSlug - Workspace slug
   * @param {string} message - Message to send
   * @param {function} onChunk - Callback for each chunk
   * @returns {Promise<void>}
   */
  async streamMessage(workspaceSlug, message, onChunk) {
    const response = await fetch(`${this.apiUrl}/api/v1/workspace/${workspaceSlug}/stream-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({ message })
    });

    if (!response.ok) {
      throw new Error(`Stream failed: ${response.status} ${response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              onChunk(data);
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  // ===== SYSTEM METHODS =====

  /**
   * Get system information
   * @returns {Promise<object>} System info
   */
  async getSystemInfo() {
    return this.request('/api/v1/system');
  }

  /**
   * Test API connection
   * @returns {Promise<boolean>} Connection status
   */
  async testConnection() {
    try {
      await this.getSystemInfo();
      return true;
    } catch (error) {
      return false;
    }
  }
}

module.exports = AnythingLLMAPI;
