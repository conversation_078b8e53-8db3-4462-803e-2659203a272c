/**
 * AnythingLLM MCP Extension
 * Model Context Protocol extensie voor AnythingLLM integratie
 */

class AnythingLLMAPI {
    constructor(apiUrl, apiKey) {
        this.apiUrl = apiUrl.replace(/\/$/, '');
        this.apiKey = apiKey;
    }

    async makeRequest(endpoint, options = {}) {
        const url = `${this.apiUrl}${endpoint}`;
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`,
            ...options.headers
        };

        const response = await fetch(url, {
            ...options,
            headers
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response.json();
    }

    async searchKnowledgeBase(query, workspaceSlug = null) {
        const endpoint = workspaceSlug
            ? `/api/v1/workspace/${workspaceSlug}/chat`
            : '/api/v1/system/search';

        return this.makeRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify({
                message: query,
                mode: 'query'
            })
        });
    }

    async sendMessage(workspaceSlug, message, mode = 'chat') {
        return this.makeRequest(`/api/v1/workspace/${workspaceSlug}/chat`, {
            method: 'POST',
            body: JSON.stringify({
                message,
                mode
            })
        });
    }

    async uploadDocument(workspaceSlug, content, filename) {
        const blob = new Blob([content], { type: 'text/plain' });
        const formData = new FormData();
        formData.append('file', blob, filename);

        const response = await fetch(`${this.apiUrl}/api/v1/workspace/${workspaceSlug}/upload`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`
            },
            body: formData
        });

        if (!response.ok) {
            throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
        }

        return response.json();
    }
}

}

let api = null;

/**
 * Initialize the MCP extension
 */
async function initialize(config) {
    if (!config.apiUrl || !config.apiKey) {
        throw new Error('API URL en API Key zijn vereist');
    }

    api = new AnythingLLMAPI(config.apiUrl, config.apiKey);
    console.log('✅ AnythingLLM MCP Extension geïnitialiseerd');
}

/**
 * MCP Tool: Search AnythingLLM Knowledge Base
 */
async function search_anythingllm(args) {
    if (!api) {
        throw new Error('Extension niet geïnitialiseerd');
    }

    const { query, workspace } = args;

    try {
        const result = await api.searchKnowledgeBase(query, workspace);

        return {
            content: [
                {
                    type: "text",
                    text: `**Zoekresultaat voor "${query}":**\n\n${result.textResponse || result.response || 'Geen resultaat gevonden'}`
                }
            ]
        };
    } catch (error) {
        return {
            content: [
                {
                    type: "text",
                    text: `❌ Fout bij zoeken: ${error.message}`
                }
            ],
            isError: true
        };
    }
}

/**
 * MCP Tool: Chat with AnythingLLM
 */
async function chat_anythingllm(args) {
    if (!api) {
        throw new Error('Extension niet geïnitialiseerd');
    }

    const { message, workspace, mode = 'chat' } = args;

    try {
        const result = await api.sendMessage(workspace, message, mode);

        return {
            content: [
                {
                    type: "text",
                    text: `**Jij:** ${message}\n\n**AnythingLLM:** ${result.textResponse || result.response || 'Geen antwoord ontvangen'}`
                }
            ]
        };
    } catch (error) {
        return {
            content: [
                {
                    type: "text",
                    text: `❌ Fout bij chatten: ${error.message}`
                }
            ],
            isError: true
        };
    }
}

/**
 * MCP Tool: Upload Document to AnythingLLM
 */
async function upload_document(args) {
    if (!api) {
        throw new Error('Extension niet geïnitialiseerd');
    }

    const { content, filename, workspace } = args;

    try {
        const result = await api.uploadDocument(workspace, content, filename);

        return {
            content: [
                {
                    type: "text",
                    text: `✅ Document "${filename}" succesvol geüpload naar workspace "${workspace}"\n\n${result.message || 'Upload voltooid'}`
                }
            ]
        };
    } catch (error) {
        return {
            content: [
                {
                    type: "text",
                    text: `❌ Fout bij uploaden: ${error.message}`
                }
            ],
            isError: true
        };
    }
}

/**
 * Export MCP tools and initialization
 */
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initialize,
        tools: {
            search_anythingllm,
            chat_anythingllm,
            upload_document
        }
    };
}
