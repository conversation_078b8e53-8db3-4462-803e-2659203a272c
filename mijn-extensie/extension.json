{"name": "AnythingLLM MCP Extensie", "description": "<PERSON><PERSON><PERSON> verbinding met AnythingLLM server via MCP", "entry": "./main.js", "version": "1.0.0", "type": "mcp-extension", "author": "Innovars", "permissions": ["http", "file-system"], "tools": [{"name": "search_anythingllm", "description": "Doorzoek de AnythingLLM knowledge base", "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "Zoekterm voor de knowledge base"}, "workspace": {"type": "string", "description": "Workspace slug (optioneel)"}}, "required": ["query"]}}, {"name": "chat_anythingllm", "description": "<PERSON><PERSON>ur een bericht naar AnythingLLM", "inputSchema": {"type": "object", "properties": {"message": {"type": "string", "description": "Be<PERSON>t om te versturen"}, "workspace": {"type": "string", "description": "Workspace slug"}, "mode": {"type": "string", "enum": ["chat", "query"], "description": "Chat mode", "default": "chat"}}, "required": ["message", "workspace"]}}, {"name": "upload_document", "description": "Upload een document naar AnythingLLM", "inputSchema": {"type": "object", "properties": {"content": {"type": "string", "description": "Document inhoud"}, "filename": {"type": "string", "description": "Bestandsnaam"}, "workspace": {"type": "string", "description": "Workspace slug"}}, "required": ["content", "filename", "workspace"]}}], "config": {"apiUrl": {"type": "string", "default": "http://localhost:3001", "description": "AnythingLLM API URL"}, "apiKey": {"type": "string", "description": "AnythingLLM API Key"}}}