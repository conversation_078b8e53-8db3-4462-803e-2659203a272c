# AnythingLLM MCP Extension

Een Model Context Protocol (MCP) extensie voor integratie met AnythingLLM. Deze extensie maakt het mogelijk om direct vanuit Claude te communiceren met je AnythingLLM knowledge base.

## 🚀 Functies

### 🔍 Knowledge Base Zoeken
- **Tool:** `search_anythingllm`
- **Beschrijving:** Doorzoek je AnythingLLM knowledge base
- **Parameters:**
  - `query` (vereist): Zoekterm
  - `workspace` (optioneel): Specifieke workspace slug

### 💬 Chat Functionaliteit  
- **Tool:** `chat_anythingllm`
- **Beschrijving:** Stuur berichten naar AnythingLLM
- **Parameters:**
  - `message` (vereist): Bericht om te versturen
  - `workspace` (vereist): Workspace slug
  - `mode` (optioneel): 'chat' of 'query' (standaard: 'chat')

### 📄 Document Upload
- **Tool:** `upload_document`
- **Beschrijving:** Upload documenten naar AnythingLLM
- **Parameters:**
  - `content` (vereist): Document inhoud
  - `filename` (vereist): Bestandsnaam
  - `workspace` (vereist): Workspace slug

## ⚙️ Configuratie

<PERSON>el de volgende configuratie in:

```json
{
  "apiUrl": "http://localhost:3001",
  "apiKey": "jouw-anythingllm-api-key"
}
```

### API Key verkrijgen:
1. Ga naar je AnythingLLM instantie
2. Navigeer naar Settings → API Keys
3. Genereer een nieuwe API key
4. Kopieer de key naar je configuratie

## 📖 Gebruik Voorbeelden

### Zoeken in Knowledge Base
```
Zoek in mijn AnythingLLM naar informatie over "machine learning"
```

### Chatten met Workspace
```
Stuur een bericht naar mijn "projecten" workspace: "Wat zijn de laatste updates?"
```

### Document Uploaden
```
Upload dit document naar mijn "documentatie" workspace:
[document inhoud hier]
```

## 🔧 Installatie

1. Plaats deze extensie in je MCP extensies directory
2. Configureer je AnythingLLM API URL en key
3. Herstart je MCP server
4. De tools zijn nu beschikbaar in Claude

## 🛠️ Technische Details

- **Type:** MCP Extension
- **Protocol:** Model Context Protocol
- **API:** AnythingLLM REST API v1
- **Authenticatie:** Bearer token

## 📝 Opmerkingen

- Zorg ervoor dat je AnythingLLM server draait en toegankelijk is
- De API key moet de juiste permissies hebben
- Workspace slugs zijn case-sensitive
- Grote documenten kunnen tijd kosten om te uploaden

## 🆘 Troubleshooting

**Verbindingsproblemen:**
- Controleer of AnythingLLM draait op de geconfigureerde URL
- Verifieer je API key
- Check firewall/netwerk instellingen

**Upload problemen:**
- Controleer workspace slug spelling
- Zorg voor voldoende storage ruimte
- Controleer bestandsformaat ondersteuning

**Zoek problemen:**
- Zorg dat de workspace documenten bevat
- Probeer verschillende zoektermen
- Check of indexering voltooid is
